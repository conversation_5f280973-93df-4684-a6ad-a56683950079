"""
Sentry test views for debugging and testing error tracking
"""
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
import logging

from .sentry_utils import capture_custom_error, add_custom_tags

logger = logging.getLogger(__name__)


@require_http_methods(["GET"])
def test_sentry_error(request):
    """
    Test endpoint to trigger a Sentry error for testing purposes.
    Only works when DEBUG=False and Sentry is configured.
    """
    if settings.DEBUG:
        return JsonResponse({
            'error': 'This endpoint only works when DEBUG=False',
            'debug': True
        }, status=400)
    
    if not hasattr(settings, 'SENTRY_DSN') or not settings.SENTRY_DSN:
        return JsonResponse({
            'error': 'Sentry is not configured',
            'configured': False
        }, status=400)
    
    try:
        add_custom_tags(
            test_type="manual_error_test",
            endpoint="test_sentry_error",
            user_agent=request.META.get('HTTP_USER_AGENT', 'unknown')
        )
        
        # Capture a custom error message
        capture_custom_error(
            "Test error from Sentry test endpoint",
            level="error",
            extra_data={
                'request_path': request.path,
                'request_method': request.method,
                'user_ip': request.META.get('REMOTE_ADDR'),
            }
        )
        
        raise ValueError("This is a test exception for Sentry integration testing")
        
    except Exception as e:
        logger.error(f"Test error triggered: {e}")
        return JsonResponse({
            'message': 'Test error sent to Sentry successfully',
            'error_type': type(e).__name__,
            'error_message': str(e)
        })


@require_http_methods(["GET"])
def sentry_status(request):
    """
    Check Sentry configuration status
    """
    sentry_configured = (
        hasattr(settings, 'SENTRY_DSN') and 
        settings.SENTRY_DSN and 
        not settings.DEBUG
    )
    
    return JsonResponse({
        'sentry_configured': sentry_configured,
        'debug_mode': settings.DEBUG,
        'has_dsn': hasattr(settings, 'SENTRY_DSN') and bool(settings.SENTRY_DSN),
        'environment': getattr(settings, 'SENTRY_ENVIRONMENT', 'not_set'),
        'message': 'Sentry is active' if sentry_configured else 'Sentry is not active'
    })


@csrf_exempt
@require_http_methods(["POST"])
def trigger_custom_error(request):
    """
    Endpoint to trigger custom errors with user-provided data
    """
    if settings.DEBUG:
        return JsonResponse({
            'error': 'This endpoint only works when DEBUG=False',
            'debug': True
        }, status=400)
    
    try:
        import json
        data = json.loads(request.body.decode('utf-8'))
        
        error_message = data.get('message', 'Custom error from API')
        error_level = data.get('level', 'error')
        extra_data = data.get('extra_data', {})
        
        # Add request context
        extra_data.update({
            'request_path': request.path,
            'request_method': request.method,
            'user_ip': request.META.get('REMOTE_ADDR'),
        })
        
        # Capture the custom error
        capture_custom_error(error_message, error_level, extra_data)
        
        return JsonResponse({
            'message': 'Custom error sent to Sentry',
            'error_message': error_message,
            'level': error_level
        })
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        logger.error(f"Error in trigger_custom_error: {e}")
        return JsonResponse({'error': str(e)}, status=500)
