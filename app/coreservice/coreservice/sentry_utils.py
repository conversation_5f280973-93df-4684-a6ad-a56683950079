"""
Sentry utility functions for testing and debugging
"""
import logging
from django.conf import settings

logger = logging.getLogger(__name__)


def test_sentry_integration():
    """
    Test function to verify Sentry is working correctly.
    This will send a test error to <PERSON><PERSON>.
    """
    try:
        # Import sentry_sdk only if it's configured
        if hasattr(settings, 'SENTRY_DSN') and settings.SENTRY_DSN and not settings.DEBUG:
            import sentry_sdk
            
            sentry_sdk.capture_message("Sentry integration test - this is a test message", level="info")
            
            try:
                1 / 0
            except ZeroDivisionError as e:
                sentry_sdk.capture_exception(e)
                
            logger.info("Sentry test completed successfully")
            return True
        else:
            logger.warning("Sentry is not configured or DEBUG is True")
            return False
            
    except Exception as e:
        logger.error(f"Error testing Sentry integration: {e}")
        return False


def capture_custom_error(message, level="error", extra_data=None):
    """
    Utility function to capture custom errors with additional context
    
    Args:
        message (str): Error message
        level (str): Error level (debug, info, warning, error, fatal)
        extra_data (dict): Additional context data
    """
    try:
        if hasattr(settings, 'SENTRY_DSN') and settings.SENTRY_DSN and not settings.DEBUG:
            import sentry_sdk
            
            if extra_data:
                with sentry_sdk.configure_scope() as scope:
                    for key, value in extra_data.items():
                        scope.set_extra(key, value)
            
            sentry_sdk.capture_message(message, level=level)
            
    except Exception as e:
        logger.error(f"Error capturing custom error to Sentry: {e}")


def add_user_context(user):
    """
    Add user context to Sentry for better error tracking
    
    Args:
        user: Django User instance
    """
    try:
        if hasattr(settings, 'SENTRY_DSN') and settings.SENTRY_DSN and not settings.DEBUG:
            import sentry_sdk
            
            with sentry_sdk.configure_scope() as scope:
                scope.user = {
                    "id": user.id,
                    "username": getattr(user, 'username', None),
                    "email": getattr(user, 'email', None),
                }
                
    except Exception as e:
        logger.error(f"Error adding user context to Sentry: {e}")


def add_custom_tags(**tags):
    """
    Add custom tags to Sentry for better error categorization
    
    Args:
        **tags: Key-value pairs of tags to add
    """
    try:
        if hasattr(settings, 'SENTRY_DSN') and settings.SENTRY_DSN and not settings.DEBUG:
            import sentry_sdk
            
            with sentry_sdk.configure_scope() as scope:
                for key, value in tags.items():
                    scope.set_tag(key, value)
                    
    except Exception as e:
        logger.error(f"Error adding custom tags to Sentry: {e}")
