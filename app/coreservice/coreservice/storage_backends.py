from django.conf import settings
from storages.backends.s3boto3 import S3Boto3Storage
from storages.backends.azure_storage import AzureStorage
from django.core.files.storage import FileSystemStorage

class AWSStaticStorage(S3Boto3Storage):
    location = 'static'
    default_acl = None


class AWSPublicMediaStorage(S3Boto3Storage):
    location = 'media'
    default_acl = None
    file_overwrite = True # True if files should be overwritten
    
class AWSPrivateMediaStorage(S3Boto3Storage):
    location = 'media'
    default_acl = None
    file_overwrite = True # True if files should be overwritten
    custom_domain = False

class AWSCategoryImageStorage(AWSPublicMediaStorage):
    location = ''
    default_acl = None

class AzureStaticStorage(AzureStorage):
    """Azure storage backend for serving static files"""

    account_name = getattr(settings, "AZURE_ACCOUNT_NAME", "")
    account_key = getattr(settings, "AZURE_ACCOUNT_KEY", "")
    azure_container = 'static-files'
    expiration_secs = None

class AzurePublicMediaStorage(AzureStorage):
    """Azure storage backend for public media files"""
    account_name = getattr(settings, "AZURE_ACCOUNT_NAME", "")
    account_key = getattr(settings, "AZURE_ACCOUNT_KEY", "")
    azure_container = 'public-media'
    expiration_secs = None

class AzurePrivateMediaStorage(AzureStorage):
    account_name = getattr(settings, "AZURE_ACCOUNT_NAME", "")
    account_key = getattr(settings, "AZURE_ACCOUNT_KEY", "")
    azure_container = 'private-media'
    expiration_secs = 600

class StaticStorage:
    """Dynamically selects the appropriate storage backend for static files at runtime."""
    def __new__(cls, *args, **kwargs):
        if getattr(settings, 'USE_S3', False):
            return AWSStaticStorage(*args, **kwargs)
        elif getattr(settings, 'USE_AZURE', False):
            return AzureStaticStorage(*args, **kwargs)
        else:
            return FileSystemStorage(*args, **kwargs)


class PublicMediaStorage:
    """Dynamically selects the appropriate storage backend for public media files at runtime."""
    def __new__(cls, *args, **kwargs):
        if getattr(settings, 'USE_S3', False):
            return AWSPublicMediaStorage(*args, **kwargs)
        elif getattr(settings, 'USE_AZURE', False):
            return AzurePublicMediaStorage(*args, **kwargs)
        else:
            return FileSystemStorage(*args, **kwargs)


class PrivateMediaStorage:
    """Dynamically selects the appropriate storage backend for private media files at runtime."""
    def __new__(cls, *args, **kwargs):
        if getattr(settings, 'USE_S3', False):
            return AWSPrivateMediaStorage(*args, **kwargs)
        elif getattr(settings, 'USE_AZURE', False):
            return AzurePrivateMediaStorage(*args, **kwargs)
        else:
            return FileSystemStorage(*args, **kwargs)
        

class CategoryImageStorage:
    """Dynamically selects the appropriate storage backend for category images at runtime."""
    def __new__(cls, *args, **kwargs):
        if getattr(settings, 'USE_S3', False):
            return AWSCategoryImageStorage(*args, **kwargs)
        else:
            return FileSystemStorage(*args, **kwargs)
