from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from authz.models.facility import Facility
from authz.models.company import CompanyMaster


class Command(BaseCommand):
    help = 'Create a test facility (default mode) or a custom facility using arguments.'

    def add_arguments(self, parser):

        parser.add_argument(
            '--force',
            action='store_true',
            help='Force creation even if facility with same code exists'
        )

    def handle(self, **options):
        try:
            with transaction.atomic():
                options = self.apply_default_test_data(options)

                company = self.get_or_create_default_company(options)

                facility_code = options.get('code') or self.generate_facility_code(options['name'])

                if not options['force'] and Facility.objects.filter(code=facility_code).exists():
                    raise CommandError(f'Facility with code "{facility_code}" already exists. Use --force to override.')

                facility_data = {
                    'name': options['name'],
                    'code': facility_code,
                    'description': options['description'],
                    'facility_type': options['facility_type'],
                    'company': company,
                    'address': options['address'],
                    'city': options['city'],
                    'state': options['state'],
                    'country': options['country'],
                    'pincode': options['pincode'],
                    'latitude': options['latitude'],
                    'longitude': options['longitude'],
                }

                facility = Facility.objects.create(**facility_data)

                self.stdout.write(
                    self.style.SUCCESS(
                        f'Created facility:\n'
                        f'  ID: {facility.id}\n'
                        f'  Name: {facility.name}\n'
                        f'  Code: {facility.code}\n'
                        f'  Company: {facility.company.name}'
                    )
                )

        except Exception as e:
            raise CommandError(f'Error creating facility: {str(e)}')

    def apply_default_test_data(self, options):
        defaults = {
            'name': 'Test Facility',
            'description': 'Test facility for development',
            'address': '123 Test Street',
            'city': 'Mumbai',
            'state': 'Maharashtra',
            'country': 'India',
            'pincode': '400001',
            'latitude': '19.0760',
            'longitude': '72.8777',
            'facility_type': 'STORE',
            'company_name': 'Test Company',
            'company_description': 'Test company for development',
        }

        for key, default_value in defaults.items():
            if options.get(key) is None:
                options[key] = default_value

        return options

    def get_or_create_default_company(self, options):
        company_name = options.get('company_name') or 'Test Company'
        company_code = options.get('company_code') or 'TESTCOMP-001'

        company, created = CompanyMaster.objects.get_or_create(
            code=company_code,
            defaults={
                'name': company_name,
                'description': options.get('company_description', ''),
            }
        )

        if created:
            self.stdout.write(f'Created company: {company.name} (Code: {company.code})')
        else:
            self.stdout.write(f'Using existing company: {company.name} (Code: {company.code})')

        return company

    def generate_facility_code(self, name):
        prefix = ''.join(c for c in name if c.isalpha())[:3].upper()
        if len(prefix) < 3:
            prefix = prefix.ljust(3, 'X')
        counter = 1
        while True:
            code = f'{prefix}-{counter:03d}'
            if not Facility.objects.filter(code=code).exists():
                return code
            counter += 1