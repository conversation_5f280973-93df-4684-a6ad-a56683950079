from rest_framework import serializers
from django.contrib.auth import authenticate
from authz.models.users import User


class LoginSerializer(serializers.Serializer):
    """
    Serializer for user login
    """
    username = serializers.Char<PERSON>ield(max_length=255)
    password = serializers.Char<PERSON>ield(max_length=128, write_only=True)
    
    def validate(self, data):
        username = data.get('username', '')
        password = data.get('password', '')
        
        if username and password:
            user = authenticate(username=username, password=password)
            if user:
                if not user.is_active:
                    raise serializers.ValidationError('User account is disabled.')
                data['user'] = user
                return data
            else:
                raise serializers.ValidationError('Unable to log in with provided credentials.')
        else:
            raise serializers.ValidationError('Must include "username" and "password".')


class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for User model
    """
    class Meta:
        model = User
        fields = ('id', 'username', 'email', 'first_name', 'last_name', 'user_type')
        read_only_fields = ('id',)
    
