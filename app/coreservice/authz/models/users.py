from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _

# Import base models - adjust the import path if necessary
from authz.models.tenant import BaseModel

class User(AbstractUser):
    """
    Extends the default User model with a new column to store the user's
    preferred language.
    """

    USER_TYPE = (
        ('sub_user', 'sub_user'),
        ('customer', 'customer'),
    )

    email = models.EmailField(_('Email address'), unique=True)
    email_verified = models.BooleanField(_('Email verified'), default=False)
    user_type = models.CharField(max_length=100, choices=USER_TYPE, default='sub_user')
    groups = models.ManyToManyField(
        'authz.CompanyGroup',
        verbose_name=_("groups"),
        related_name='users',
        blank=True,
    )

    def __str__(self):
        return self.username

    def has_role(self, role_name):
        """Check if the user has a specific role"""
        return self.roles.filter(name=role_name).exists()

    def get_facilities(self):
        return self.user_facility_mappings.values_list('facility', flat=True)



class UserDetails(BaseModel):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='user_details')
    address = models.CharField(max_length=100)
    phone = models.CharField(max_length=15)
    date_of_birth = models.DateField(null=True, blank=True)

    def __str__(self):
        return self.user.username


class UserFacilityMapping(BaseModel):

    # STATUS is a tuple of choices for the status field.
    # The first element of each tuple is the value that will be stored in the database,
    # and the second element is the human-readable name for the choice.
    # For example, if the status field is set to 0, the value 'Active' will be displayed to the user.
    STATUS = (
        (0, _('Active')),
        (1, _('Inactive')),
    )

    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='user_facility_mappings')
    facility = models.ForeignKey('Facility', on_delete=models.CASCADE, related_name='user_facility_mappings')
    status = models.IntegerField(choices=STATUS)

    def __str__(self):
        return f"{self.user} - {self.facility}"
        
