from django.db import models
from django_multitenant.mixins import TenantModelMixin
from authz.models.tenant import BaseModel

class Facility(TenantModelMixin, BaseModel):

    facility_types = [
        ('STORE', 'Store'),
        ('WAREHOUSE', 'Warehouse'),
        ('DISTRIBUTION_CENTER', 'Distribution Center'),
    ]

    tenant_id = 'id'
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=100)
    description = models.TextField()
    facility_type = models.CharField(max_length=100, choices=facility_types)
    company = models.ForeignKey('CompanyMaster', on_delete=models.CASCADE, related_name='facilities_company')
    address = models.CharField(max_length=100)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    country = models.CharField(max_length=100)
    pincode = models.CharField(max_length=100)
    latitude = models.CharField(max_length=100)
    longitude = models.Char<PERSON>ield(max_length=100)


    def __str__(self) -> str:
        return self.name
