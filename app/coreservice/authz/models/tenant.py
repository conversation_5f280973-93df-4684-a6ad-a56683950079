from django.db import models
from django_multitenant.mixins import TenantModelMixin
from django.db.models import ForeignKey, AutoField


class BaseModel(models.Model):
    id = AutoField(primary_key=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

class TenantModel(BaseModel, TenantModelMixin):
    facility = ForeignKey('authz.Facility', on_delete=models.CASCADE, related_name='%(class)s_account', db_constraint=False, blank=True)
    tenant_id = "facility_id"
    created_by = models.ForeignKey('authz.User', related_name="%(class)s_created_by", null=True, blank=True, on_delete=models.SET_NULL)
    updated_by = models.ForeignKey('authz.User', related_name="%(class)s_updated_by", null=True, blank=True, on_delete=models.SET_NULL)

    class Meta:
        abstract = True
        unique_together = ('id', 'facility_id')

    class TenantMeta:
        tenant_field_name = 'facility_id'