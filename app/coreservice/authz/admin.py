from django.contrib import admin

# Register your models here.
from authz.models.users import User, UserDetails, UserFacilityMapping
from authz.models.facility import Facility
from authz.models.roles import CompanyGroup
from authz.models.company import CompanyMaster

from django.contrib.auth.admin import UserAdmin as BaseUserAdmin


class UserAdmin(BaseUserAdmin):
    list_display = ('username', 'email', 'first_name', 'last_name', 'is_staff')
    filter_horizontal = ('groups', 'user_permissions')
    
    fieldsets = (
        ('Personal Information', {
            'fields': (
                'username',
                'first_name',
                'last_name',
                'password',
                'email',
                'email_verified'
            )
        }),
        ('Permissions', {
            'fields': (
                'is_active',
                'is_staff',
                'is_superuser',
                'groups',
                'user_permissions'
            )
        }),
        ('Important Dates', {
            'fields': (
                'last_login',
                'date_joined'
            )
        })
    )

# class Admin(admin.ModelAdmin):
#     list_display = ('name', 'code_name', 'description')
#     search_fields = ('name', 'code_name')
#     filter_horizontal = ('permissions',)
#     fieldsets = (
#         (None, {
#             'fields': ('name', 'code_name', 'description', 'company')
#         }),
#         ('Permissions', {
#             'fields': ('permissions',)
#         }),
#     )

admin.site.register(User, UserAdmin)
admin.site.register(UserDetails)
admin.site.register(CompanyGroup)
admin.site.register(Facility)
admin.site.register(CompanyMaster)
admin.site.register(UserFacilityMapping)
