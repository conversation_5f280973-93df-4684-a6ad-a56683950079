from django.db import models
from authz.models.tenant import TenantModel
from products.models.product import Product


class Order(TenantModel):
    order_number = models.CharField(max_length=100)
    order_date = models.DateTimeField(auto_now_add=True)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=100, default='Pending')
    customer_name = models.CharField(max_length=255)
    customer_reference = models.CharField(max_length=100, blank=True, null=True)
    customer_address = models.TextField()
    order_type = models.CharField(max_length=50, default='Standard')
    slot_from = models.CharField(max_length=8, blank=True, null=True)  # HH:MM:SS
    slot_to = models.CharField(max_length=8, blank=True, null=True)  # HH:MM:SS
    promised_time = models.DateTimeField(blank=True, null=True)

    def __str__(self):
        return self.order_number


class OrderLineItem(TenantModel):
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='order_line_items')
    product = models.ForeignKey(Product, on_delete=models.PROTECT, related_name='order_items')
    quantity = models.IntegerField()
    price = models.DecimalField(max_digits=10, decimal_places=2)  # sale_price_gst
    mrp = models.DecimalField(max_digits=10, decimal_places=2)
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    def __str__(self):
        return f"{self.order.order_number} - {self.product.name if self.product else 'Unknown'}"
