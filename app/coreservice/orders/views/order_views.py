import json
from datetime import datetime
from decimal import Decimal

from django.http import JsonResponse
from django.db import transaction
from django.utils import timezone
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

from orders.models.order import Order, OrderLineItem
from orders.validators.order_validators import OrderValidator
from products.models.product import Product
from authz.models.facility import Facility


class OrderAPIView(APIView):
    """
    API view for handling order operations
    """
    
    def get(self, request, order_id=None):
        """
        Get order details by ID or list all orders
        """
        if order_id:
            try:
                order = Order.objects.get(id=order_id)
                return self._get_order_response(order)
            except Order.DoesNotExist:
                return Response(
                    {"error": f"Order with ID {order_id} not found"},
                    status=status.HTTP_404_NOT_FOUND
                )
        else:
            # List all orders with pagination
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 10))
            start = (page - 1) * page_size
            end = start + page_size
            
            # Apply filters if provided
            filters = {}
            if 'status' in request.query_params:
                filters['status'] = request.query_params['status']
            if 'order_number' in request.query_params:
                filters['order_number'] = request.query_params['order_number']
            
            orders = Order.objects.filter(**filters).order_by('-order_date')[start:end]
            total_count = Order.objects.filter(**filters).count()
            
            response_data = {
                "count": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": (total_count + page_size - 1) // page_size,
                "results": [self._get_order_data(order) for order in orders]
            }
            
            return Response(response_data)
    
    def post(self, request):
        """
        Create a new order
        """
        try:
            data = request.data
            
            # Validate order data
            validator = OrderValidator(data)
            if not validator.is_valid():
                return Response(
                    {"errors": validator.errors},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Process the order
            with transaction.atomic():
                order = self._create_order(data)
                self._create_order_items(order, data.get('items', []))
            
            return Response(
                {"message": "Order created successfully", "order_id": order.id},
                status=status.HTTP_201_CREATED
            )
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def put(self, request, order_id):
        """
        Update an existing order
        """
        try:
            data = request.data
            
            # Check if order exists
            try:
                order = Order.objects.get(id=order_id)
            except Order.DoesNotExist:
                return Response(
                    {"error": f"Order with ID {order_id} not found"},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Validate order data
            validator = OrderValidator(data)
            if not validator.is_valid():
                return Response(
                    {"errors": validator.errors},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Update the order
            with transaction.atomic():
                self._update_order(order, data)
                self._update_order_items(order, data.get('items', []))
            
            return Response(
                {"message": "Order updated successfully"},
                status=status.HTTP_200_OK
            )
            
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _create_order(self, data):
        """
        Create a new order from validated data
        """
        # Get facility from facility code
        facility = Facility.objects.get(code=data.get('facility'))
        
        # Create customer address string
        customer_data = data.get('customer', {})
        address = f"{customer_data.get('address_line_1', '')}"
        if customer_data.get('address_line_2'):
            address += f", {customer_data.get('address_line_2')}"
        address += f", {customer_data.get('city', '')}, {customer_data.get('state', '')} - {customer_data.get('pincode', '')}"
        
        # Calculate total amount from items
        total_amount = Decimal('0.00')
        for item in data.get('items', []):
            quantity = item.get('quantity', 0)
            price = Decimal(str(item.get('sale_price_gst', 0)))
            total_amount += quantity * price
        
        # Parse promised time
        promised_time = None
        if data.get('promised_time'):
            promised_time = datetime.strptime(data.get('promised_time'), '%Y-%m-%d %H:%M:%S')
        
        # Create order
        order = Order.objects.create(
            order_number=data.get('order_reference'),
            facility=facility,
            total_amount=total_amount,
            status='Pending',  # Default status
            customer_name=customer_data.get('customer_name', ''),
            customer_reference=customer_data.get('customer_reference', ''),
            customer_address=address,
            order_type=data.get('order_type', 'Standard'),
            slot_from=data.get('slot_from'),
            slot_to=data.get('slot_to'),
            promised_time=promised_time
        )
        
        return order
    
    def _create_order_items(self, order, items_data):
        """
        Create order line items for a new order
        """
        for item_data in items_data:
            # Get product by SKU
            product = Product.objects.get(sku=item_data.get('sku'))
            
            # Create order line item
            OrderLineItem.objects.create(
                order=order,
                facility=order.facility,
                product=product,
                quantity=item_data.get('quantity', 0),
                price=Decimal(str(item_data.get('sale_price_gst', 0))),
                mrp=Decimal(str(item_data.get('mrp', 0))),
                discount_amount=Decimal(str(item_data.get('discount_amount', 0)))
            )
    
    def _update_order(self, order, data):
        """
        Update an existing order
        """
        # Update order fields if provided
        if 'order_reference' in data:
            order.order_number = data['order_reference']
        
        if 'facility' in data:
            facility = Facility.objects.get(code=data['facility'])
            order.facility = facility
        
        if 'order_type' in data:
            order.order_type = data['order_type']
        
        if 'slot_from' in data:
            order.slot_from = data['slot_from']
        
        if 'slot_to' in data:
            order.slot_to = data['slot_to']
        
        if 'promised_time' in data:
            promised_time = datetime.strptime(data['promised_time'], '%Y-%m-%d %H:%M:%S')
            order.promised_time = promised_time
        
        # Update customer information if provided
        customer_data = data.get('customer', {})
        if customer_data:
            if 'customer_name' in customer_data:
                order.customer_name = customer_data['customer_name']
            
            if 'customer_reference' in customer_data:
                order.customer_reference = customer_data['customer_reference']
            
            # Update address if any address field is provided
            address_fields = ['address_line_1', 'address_line_2', 'city', 'state', 'pincode']
            if any(field in customer_data for field in address_fields):
                address = f"{customer_data.get('address_line_1', '')}"
                if customer_data.get('address_line_2'):
                    address += f", {customer_data.get('address_line_2')}"
                address += f", {customer_data.get('city', '')}, {customer_data.get('state', '')} - {customer_data.get('pincode', '')}"
                order.customer_address = address
        
        # Recalculate total amount if items are provided
        if 'items' in data:
            total_amount = Decimal('0.00')
            for item in data['items']:
                quantity = item.get('quantity', 0)
                price = Decimal(str(item.get('sale_price_gst', 0)))
                total_amount += quantity * price
            order.total_amount = total_amount
        
        order.save()
        return order
    
    def _update_order_items(self, order, items_data):
        """
        Update order line items for an existing order
        """
        # Remove existing items
        OrderLineItem.objects.filter(order=order).delete()
        
        # Create new items
        self._create_order_items(order, items_data)
    
    def _get_order_response(self, order):
        """
        Generate response for a single order
        """
        return Response(self._get_order_data(order))
    
    def _get_order_data(self, order):
        """
        Convert order object to dictionary for response
        """
        # Get order line items
        line_items = OrderLineItem.objects.filter(order=order)
        items_data = []
        
        for item in line_items:
            items_data.append({
                'sku': item.product.sku if item.product else '',
                'name': item.product.name if item.product else '',
                'quantity': item.quantity,
                'mrp': float(item.mrp),
                'sale_price_gst': float(item.price),
                'discount_amount': float(item.discount_amount) if item.discount_amount else 0.0
            })
        
        # Extract customer data from address
        customer_data = {
            'customer_reference': order.customer_reference,
            'customer_name': order.customer_name,
            'address': order.customer_address
        }
        
        # Format promised time
        promised_time = None
        if order.promised_time:
            promised_time = order.promised_time.strftime('%Y-%m-%d %H:%M:%S')
        
        return {
            'id': order.id,
            'order_reference': order.order_number,
            'facility': order.facility.code if order.facility else '',
            'facility_name': order.facility.name if order.facility else '',
            'customer': customer_data,
            'items': items_data,
            'order_type': order.order_type,
            'slot_from': order.slot_from,
            'slot_to': order.slot_to,
            'promised_time': promised_time,
            'total_amount': float(order.total_amount),
            'status': order.status,
            'created_at': order.creation_date.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': order.updation_date.strftime('%Y-%m-%d %H:%M:%S')
        }


class OrderStatusAPIView(APIView):
    """
    API view for updating order status
    """
    
    def put(self, request, order_id):
        """
        Update order status
        """
        try:
            # Check if order exists
            try:
                order = Order.objects.get(id=order_id)
            except Order.DoesNotExist:
                return Response(
                    {"error": f"Order with ID {order_id} not found"},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Validate status
            new_status = request.data.get('status')
            if not new_status:
                return Response(
                    {"error": "Status is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            valid_statuses = ['Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled']
            if new_status not in valid_statuses:
                return Response(
                    {"error": f"Invalid status. Must be one of: {', '.join(valid_statuses)}"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Update status
            order.status = new_status
            order.save()
            
            return Response(
                {"message": f"Order status updated to {new_status}"},
                status=status.HTTP_200_OK
            )
            
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
