products_schema = {
        'name': 'products',
         'fields': [
                {'name': 'id', 'type': 'string'},
                {'name': 'name', 'type': 'string'},
                {'name': 'description', 'type': 'string'},
                {'name': 'sku', 'type': 'string'},
                {'name': 'price', 'type': 'float'},
                {'name': 'brand', 'type': 'string', 'optional': True, 'facet': True},
                {'name': 'is_active', 'type': 'bool'},
                {'name': 'facility_id', 'type': 'string', 'facet': True},
                {'name': 'facility_name', 'type': 'string', 'facet': True},
                {'name': 'categories', 'type': 'string[]', 'facet': True},
                {'name': 'subcategories', 'type': 'string[]', 'facet': True},
                {'name': 'created_at', 'type': 'string'},
                {'name': 'updated_at', 'type': 'string'},
                {'name': 'weight', 'type': 'float', 'optional': True},
                {'name': 'weight_unit', 'type': 'string', 'optional': True},
                {'name': 'image_url', 'type': 'string[]', 'optional': True},
                {'name': 'thumbnail_url', 'type': 'string', 'optional': True},
                {'name': 'display_alias', 'type': 'string[]', 'optional': True},
                {'name': 'tags', 'type': 'string[]', 'optional': True, 'facet': True},
                {'name': 'colour', 'type': 'string', 'optional': True},
            ],
            "token_separators": ['-', ' ']
    }

categories_schema = {
        'name': 'categories',
        'fields': [
            {'name': 'id', 'type': 'string'},
            {'name': 'name', 'type': 'string'},
            {'name': 'description', 'type': 'string', 'optional': True},
            {'name': 'code', 'type': 'string'},
            {'name': 'is_active', 'type': 'bool'},
            {'name': 'thumbnail_url', 'type': 'string'},
            {'name': 'section', 'type': 'string', 'facet': True},
        ],
    }

subcategories_schema = {
        'name': 'subcategories',
        'fields': [
            {'name': 'id', 'type': 'string'},
            {'name': 'name', 'type': 'string'},
            {'name': 'description', 'type': 'string', 'optional': True},
            {'name': 'code', 'type': 'string'},
            {'name': 'category_id', 'type': 'int32', 'facet': True},
            {'name': 'category_name', 'type': 'string', 'facet': True},
            {'name': 'is_active', 'type': 'bool'},
            {'name': 'thumbnail_url', 'type': 'string'},
        ],
}

sections_schema = {
    'name': 'sections',
    'fields': [
        {'name': 'id', 'type': 'string'},
        {'name': 'name', 'type': 'string'},
        {'name': 'slug', 'type': 'string', 'optional': True},
        {'name': 'section_type', 'type': 'string', 'facet': True},
        {'name': 'context', 'type': 'string', 'facet': True},
        {'name': 'priority', 'type': 'int32'},
        {'name': 'display_limit', 'type': 'int32'},
        {'name': 'tags', 'type': 'string[]', 'optional': True, 'facet': True},
        {'name': 'brands', 'type': 'string[]', 'optional': True, 'facet': True},
        {'name': 'background_color', 'type': 'string', 'optional': True},
        {'name': 'text_color', 'type': 'string', 'optional': True},
        {'name': 'metadata', 'type': 'string', 'optional': True},  # Store as JSON string instead of object
        {'name': 'is_active', 'type': 'bool'},
        {'name': 'start_date', 'type': 'string', 'optional': True},
        {'name': 'end_date', 'type': 'string', 'optional': True},
        {'name': 'created_at', 'type': 'string'},
        {'name': 'updated_at', 'type': 'string'},
        {'name': 'product_count', 'type': 'int32'},
        {'name': 'category_count', 'type': 'int32'},
        {'name': 'subcategory_count', 'type': 'int32'},
        {'name': 'product_names', 'type': 'string[]', 'optional': True},
        {'name': 'category_names', 'type': 'string[]', 'optional': True, 'facet': True},
        {'name': 'subcategory_names', 'type': 'string[]', 'optional': True, 'facet': True},
    ],
    "token_separators": ['-', ' ']
}