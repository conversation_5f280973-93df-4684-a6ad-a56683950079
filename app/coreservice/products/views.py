from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.http import JsonResponse
from products.models import Product, Category, Subcategory, Section
from products.serializers import (
    ProductSerializer, ProductSearchSerializer, CategorySerializer,
    SubcategorySerializer, SectionSerializer, SectionSearchSerializer
)
from products.typesense.client import get_typesense_client
import json


class CategoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint for product categories
    """
    queryset = Category.objects.all()
    serializer_class = CategorySerializer


class SubcategoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint for product subcategories
    """
    queryset = Subcategory.objects.all()
    serializer_class = SubcategorySerializer
    
    def get_queryset(self):
        queryset = Subcategory.objects.all()
        category_id = self.request.query_params.get('category_id', None)
        if category_id is not None:
            queryset = queryset.filter(category_id=category_id)
        return queryset


class ProductViewSet(viewsets.ModelViewSet):
    """
    API endpoint for products
    """
    queryset = Product.objects.all()
    serializer_class = ProductSerializer
    
    def get_queryset(self):
        queryset = Product.objects.all()
        facility_id = self.request.query_params.get('facility_id', None)
        if facility_id is not None:
            queryset = queryset.filter(facility_id=facility_id)
        return queryset

    @action(detail=False, methods=['post'])
    def search(self, request):
        """
        Search products using Typesense
        """
        serializer = ProductSearchSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        validated_data = serializer.validated_data
        query = validated_data.get('query', '')
        category = validated_data.get('category', '')
        subcategory = validated_data.get('subcategory', '')
        facility_id = validated_data.get('facility_id', '')
        brand = validated_data.get('brand', '')
        min_price = validated_data.get('min_price')
        max_price = validated_data.get('max_price')
        sort_by = validated_data.get('sort_by', 'name')
        sort_order = validated_data.get('sort_order', 'asc')
        
        # Build search parameters
        search_params = {
            'q': query,
            'query_by': 'name,description,sku',
            'sort_by': f'{sort_by}:{sort_order}',
            'per_page': 100,
            'filter_by': '',
            'facet_by': 'categories,subcategories,brand,facility_name'
        }
        
        # Add filters if provided
        filters = []
        if category:
            filters.append(f'categories:=[{category}]')
        if subcategory:
            filters.append(f'subcategories:=[{subcategory}]')
        if facility_id:
            filters.append(f'facility_id:={facility_id}')
        if brand:
            filters.append(f'brand:={brand}')
        if min_price is not None:
            filters.append(f'price:>={min_price}')
        if max_price is not None:
            filters.append(f'price:<={max_price}')
        
        if filters:
            search_params['filter_by'] = ' && '.join(filters)
        
        try:
            client = get_typesense_client()
            search_results = client.collections['products'].documents.search(search_params)
            return Response(search_results)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SectionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for sections - Read-only with Typesense search
    """
    queryset = Section.objects.filter(is_active=True).order_by('priority')
    serializer_class = SectionSerializer
    permission_classes = [AllowAny]  # Allow unauthenticated access for demo

    def get_queryset(self):
        """Filter sections by context if provided"""
        queryset = Section.objects.filter(is_active=True).order_by('priority')
        context = self.request.query_params.get('context', None)
        if context:
            queryset = queryset.filter(context=context.upper())
        return queryset

    @action(detail=False, methods=['post'])
    def search(self, request):
        """
        Search sections using Typesense
        """
        serializer = SectionSearchSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        validated_data = serializer.validated_data
        query = validated_data.get('query', '')
        section_type = validated_data.get('section_type', '')
        context = validated_data.get('context', '')
        tags = validated_data.get('tags', '')
        brands = validated_data.get('brands', '')
        is_active = validated_data.get('is_active', True)
        sort_by = validated_data.get('sort_by', 'priority')
        sort_order = validated_data.get('sort_order', 'asc')
        per_page = validated_data.get('per_page', 20)

        # Build search parameters
        search_params = {
            'q': query or '*',
            'query_by': 'name,category_names,product_names,subcategory_names',
            'sort_by': f'{sort_by}:{sort_order}',
            'per_page': per_page,
            'filter_by': '',
            'facet_by': 'section_type,context,tags,brands,category_names'
        }

        # Add filters
        filters = []
        if is_active is not None:
            filters.append(f'is_active:={str(is_active).lower()}')
        if section_type:
            filters.append(f'section_type:={section_type}')
        if context:
            filters.append(f'context:={context}')
        if tags:
            filters.append(f'tags:=[{tags}]')
        if brands:
            filters.append(f'brands:=[{brands}]')

        if filters:
            search_params['filter_by'] = ' && '.join(filters)

        try:
            client = get_typesense_client()
            search_results = client.collections['sections'].documents.search(search_params)
            return Response(search_results)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def homepage(self, request):
        """
        Get homepage sections with their products from Typesense
        """
        try:
            client = get_typesense_client()

            # Search for homepage sections
            search_params = {
                'q': '*',
                'query_by': 'name',
                'sort_by': 'priority:asc',
                'per_page': 50,
                'filter_by': 'context:=HOMEPAGE && is_active:=true'
            }

            sections_result = client.collections['sections'].documents.search(search_params)
            sections_data = []

            for section_hit in sections_result.get('hits', []):
                section = section_hit['document']

                # Parse metadata back from JSON string
                try:
                    section['metadata'] = json.loads(section.get('metadata', '{}'))
                except (json.JSONDecodeError, TypeError):
                    section['metadata'] = {}

                # Get products for this section based on section type
                section_products = self._get_section_products(client, section)
                section['products'] = section_products

                sections_data.append(section)

            return Response({
                'sections': sections_data,
                'total_sections': len(sections_data)
            })

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_section_products(self, client, section):
        """
        Get products for a section based on its type and configuration
        """
        try:
            display_limit = section.get('display_limit', 10)
            section_type = section.get('section_type', '')

            # Build product search based on section type
            if section_type == 'CATEGORY_BASED':
                category_names = section.get('category_names', [])
                if category_names:
                    filter_by = f'categories:=[{",".join(category_names)}]'
                else:
                    return []
            elif section_type == 'TAG_BASED':
                tags = section.get('tags', [])
                if tags:
                    filter_by = f'tags:=[{",".join(tags)}]'
                else:
                    return []
            elif section_type == 'BRAND_BASED':
                brands = section.get('brands', [])
                if brands:
                    filter_by = f'brand:=[{",".join(brands)}]'
                else:
                    return []
            else:
                # For CURATED, ALGORITHMIC, PROMOTIONAL - get random products
                filter_by = 'is_active:=true'

            # Search products
            product_search_params = {
                'q': '*',
                'query_by': 'name',
                'sort_by': 'name:asc',
                'per_page': display_limit,
                'filter_by': filter_by
            }

            products_result = client.collections['products'].documents.search(product_search_params)
            return [hit['document'] for hit in products_result.get('hits', [])]

        except Exception as e:
            print(f"Error getting section products: {e}")
            return []


@csrf_exempt
def homepage_sections(request):
    """
    Get homepage sections with their products from Typesense - No authentication required
    """
    try:
        client = get_typesense_client()

        # Search for homepage sections
        search_params = {
            'q': '*',
            'query_by': 'name',
            'sort_by': 'priority:asc',
            'per_page': 50,
            'filter_by': 'context:=HOMEPAGE && is_active:=true'
        }

        sections_result = client.collections['sections'].documents.search(search_params)
        sections_data = []

        for section_hit in sections_result.get('hits', []):
            section = section_hit['document']

            # Parse metadata back from JSON string
            try:
                section['metadata'] = json.loads(section.get('metadata', '{}'))
            except (json.JSONDecodeError, TypeError):
                section['metadata'] = {}

            # Get products for this section based on section type
            section_products = _get_section_products_helper(client, section)
            section['products'] = section_products

            sections_data.append(section)

        return JsonResponse({
            'sections': sections_data,
            'total_sections': len(sections_data)
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


def _get_section_products_helper(client, section):
    """
    Helper function to get products for a section based on its type and configuration
    """
    try:
        display_limit = section.get('display_limit', 10)
        section_type = section.get('section_type', '')
        section_name = section.get('name', 'Unknown')

        # For CURATED sections, use the product_names if available
        if section_type == 'CURATED':
            product_names = section.get('product_names', [])
            if product_names:
                # Search for products by name
                products = []
                for product_name in product_names[:display_limit]:
                    try:
                        search_params = {
                            'q': product_name,
                            'query_by': 'name',
                            'per_page': 1,
                            'filter_by': 'is_active:=true'
                        }
                        result = client.collections['products'].documents.search(search_params)
                        if result.get('hits'):
                            products.append(result['hits'][0]['document'])
                    except Exception as e:
                        continue
                return products

        # Build product search based on section type
        if section_type == 'CATEGORY_BASED':
            category_names = section.get('category_names', [])
            if category_names:
                # Use proper Typesense filter syntax for array fields
                category_filters = ' || '.join([f'categories:={cat}' for cat in category_names])
                filter_by = f'({category_filters}) && is_active:=true'
            else:
                filter_by = 'is_active:=true'
        elif section_type == 'TAG_BASED':
            tags = section.get('tags', [])
            if tags:
                # Use proper Typesense filter syntax for array fields
                tag_filters = ' || '.join([f'tags:={tag}' for tag in tags])
                filter_by = f'({tag_filters}) && is_active:=true'
            else:
                filter_by = 'is_active:=true'
        elif section_type == 'BRAND_BASED':
            brands = section.get('brands', [])
            if brands:
                # Use proper Typesense filter syntax for string fields
                brand_filters = ' || '.join([f'brand:={brand}' for brand in brands])
                filter_by = f'({brand_filters}) && is_active:=true'
            else:
                filter_by = 'is_active:=true'
        else:
            # For ALGORITHMIC, PROMOTIONAL - get random products
            filter_by = 'is_active:=true'

        # Search products
        product_search_params = {
            'q': '*',
            'query_by': 'name',
            'sort_by': 'price:asc',
            'per_page': display_limit,
            'filter_by': filter_by
        }

        products_result = client.collections['products'].documents.search(product_search_params)
        products = [hit['document'] for hit in products_result.get('hits', [])]
        return products

    except Exception as e:
        return []


def test_endpoint(request):
    """
    Simple test endpoint
    """
    return JsonResponse({'message': 'Hello from test endpoint', 'status': 'success'})


def ecommerce_demo(request):
    """
    Simple ecommerce demo page - accessible to all users
    """
    return render(request, 'ecommerce/demo.html')
