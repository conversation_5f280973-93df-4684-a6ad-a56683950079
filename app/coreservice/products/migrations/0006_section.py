# Generated by Django 5.2.3 on 2025-07-01 12:03

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0005_alter_productimage_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Section',
            fields=[
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('slug', models.SlugField(blank=True, max_length=255, unique=True)),
                ('section_type', models.CharField(choices=[('CURATED', 'Curated'), ('CATEGORY_BASED', 'Category Based'), ('TAG_BASED', 'Tag Based'), ('BRAND_BASED', 'Brand Based'), ('ALGORITHMIC', 'Algorithmic'), ('PROMOTIONAL', 'Promotional')], max_length=20)),
                ('context', models.CharField(choices=[('HOMEPAGE', 'Homepage'), ('CATEGORY_PAGE', 'Category Page'), ('PRODUCT_PAGE', 'Product Page'), ('CART_PAGE', 'Cart Page'), ('FOOTER', 'Footer'), ('NAVIGATION', 'Navigation')], default='HOMEPAGE', max_length=20)),
                ('priority', models.PositiveIntegerField()),
                ('display_limit', models.PositiveIntegerField(default=10)),
                ('is_active', models.BooleanField(default=True)),
                ('start_date', models.DateTimeField(blank=True, null=True)),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('tags', models.JSONField(blank=True, default=list)),
                ('brands', models.JSONField(blank=True, default=list)),
                ('background_color', models.CharField(blank=True, max_length=7, null=True)),
                ('text_color', models.CharField(blank=True, max_length=7, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('categories', models.ManyToManyField(blank=True, related_name='sections', to='products.category')),
                ('products', models.ManyToManyField(blank=True, related_name='sections', to='products.product')),
                ('subcategories', models.ManyToManyField(blank=True, related_name='sections', to='products.subcategory')),
            ],
            options={
                'verbose_name': 'Homepage Section',
                'verbose_name_plural': 'Homepage Sections',
                'ordering': ['priority', 'name'],
            },
        ),
    ]
