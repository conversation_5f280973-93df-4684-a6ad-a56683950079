# Generated by Django 5.2.3 on 2025-06-23 13:01

import coreservice.storage_backends
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0004_category_thumbnail_image_product_thumbnail_image_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='productimage',
            name='alt_text',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='productimage',
            name='priority',
            field=models.PositiveIntegerField(default=1),
        ),
        migrations.AlterModelOptions(
            name='productimage',
            options={'ordering': ['priority', 'id'], 'verbose_name': 'Product Image', 'verbose_name_plural': 'Product Images'},
        ),
        migrations.AlterUniqueTogether(
            name='productimage',
            unique_together={('product', 'priority')},
        ),
        migrations.AlterField(
            model_name='category',
            name='thumbnail_image',
            field=models.ImageField(blank=True, help_text='Upload image file. Original stored with filename, WebP versions generated automatically.', null=True, storage=coreservice.storage_backends.AWSCategoryImageStorage(), upload_to='Category/'),
        ),
        migrations.AlterField(
            model_name='product',
            name='thumbnail_image',
            field=models.ImageField(blank=True, help_text='Upload an image file. Will be converted to WebP format and stored in S3.', null=True, storage=coreservice.storage_backends.AWSCategoryImageStorage(), upload_to='Product/'),
        ),
        migrations.AlterField(
            model_name='productimage',
            name='image',
            field=models.ImageField(blank=True, help_text='Upload an image file. Will be converted to WebP format (500x500, 600x600, 800x800) and stored in S3.', null=True, storage=coreservice.storage_backends.AWSCategoryImageStorage(), upload_to='ProductImages/'),
        ),
        migrations.AlterField(
            model_name='productimage',
            name='is_primary',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='subcategory',
            name='thumbnail_image',
            field=models.ImageField(blank=True, help_text='Upload image file. Original stored with filename, WebP versions generated automatically.', null=True, storage=coreservice.storage_backends.AWSCategoryImageStorage(), upload_to='Subcategory/'),
        ),
        migrations.RemoveField(
            model_name='productimage',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='productimage',
            name='display_order',
        ),
        migrations.RemoveField(
            model_name='productimage',
            name='facility',
        ),
        migrations.RemoveField(
            model_name='productimage',
            name='updated_by',
        ),
    ]
