import json
import logging
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from products.models.product import Product
from products.models.category import Category, Subcategory
from products.typesense.client import get_typesense_client

logger = logging.getLogger(__name__)


def handle_webhook_event(operation, table, record, old_record):
    """Common webhook event handler"""
    try:
        client = get_typesense_client()

        if operation in ['insert', 'update']:
            # Handle insert/update operations
            if table == 'products_product':
                try:
                    product = Product.objects.get(id=record['id'])
                    document = product.to_typesense_dict()
                    client.collections['products'].documents.upsert(document)
                    logger.info(f"Product {product.id} indexed in Typesense via Sequin")
                    return True
                except Product.DoesNotExist:
                    logger.warning(f"Product {record['id']} not found for Typesense indexing")
                    return False

            elif table == 'products_category':
                try:
                    category = Category.objects.get(id=record['id'])
                    document = category.to_typesense_dict()
                    client.collections['categories'].documents.upsert(document)
                    logger.info(f"Category {category.id} indexed in Typesense via Sequin")
                    return True
                except Category.DoesNotExist:
                    logger.warning(f"Category {record['id']} not found for Typesense indexing")
                    return False

            elif table == 'products_subcategory':
                try:
                    subcategory = Subcategory.objects.get(id=record['id'])
                    document = subcategory.to_typesense_dict()
                    client.collections['subcategories'].documents.upsert(document)
                    logger.info(f"Subcategory {subcategory.id} indexed in Typesense via Sequin")
                    return True
                except Subcategory.DoesNotExist:
                    logger.warning(f"Subcategory {record['id']} not found for Typesense indexing")
                    return False

            elif table == 'products_product_subcategories':
                # Handle M2M relationship changes
                product_id = record.get('product_id')
                if product_id:
                    try:
                        product = Product.objects.get(id=product_id)
                        document = product.to_typesense_dict()
                        client.collections['products'].documents.upsert(document)
                        logger.info(f"Product {product.id} updated in Typesense due to M2M change via Sequin")
                        return True
                    except Product.DoesNotExist:
                        logger.warning(f"Product {product_id} not found for M2M update")
                        return False

        elif operation == 'delete':
            # Handle delete operations
            if table == 'products_product':
                try:
                    client.collections['products'].documents[str(record['id'])].delete()
                    logger.info(f"Product {record['id']} deleted from Typesense via Sequin")
                    return True
                except Exception as e:
                    logger.error(f"Error deleting product {record['id']} from Typesense: {e}")
                    return False

            elif table == 'products_category':
                try:
                    client.collections['categories'].documents[str(record['id'])].delete()
                    logger.info(f"Category {record['id']} deleted from Typesense via Sequin")
                    return True
                except Exception as e:
                    logger.error(f"Error deleting category {record['id']} from Typesense: {e}")
                    return False

            elif table == 'products_subcategory':
                try:
                    client.collections['subcategories'].documents[str(record['id'])].delete()
                    logger.info(f"Subcategory {record['id']} deleted from Typesense via Sequin")
                    return True
                except Exception as e:
                    logger.error(f"Error deleting subcategory {record['id']} from Typesense: {e}")
                    return False

        return False

    except Exception as e:
        logger.error(f"Error handling webhook event: {e}")
        return False


@csrf_exempt
@require_http_methods(["POST"])
def product_webhook(request):
    """Handle Product model changes from Sequin"""
    try:
        payload = json.loads(request.body)
        operation = payload.get('operation')
        table = payload.get('table')
        record = payload.get('record', {})
        old_record = payload.get('old_record', {})

        result = handle_webhook_event(operation, table, record, old_record)

        if result:
            return JsonResponse({'status': 'success'})
        else:
            return JsonResponse({'status': 'error'}, status=500)

    except Exception as e:
        logger.error(f"Error processing product webhook: {e}")
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def category_webhook(request):
    """Handle Category model changes from Sequin"""
    try:
        payload = json.loads(request.body)
        operation = payload.get('operation')
        table = payload.get('table')
        record = payload.get('record', {})
        old_record = payload.get('old_record', {})

        result = handle_webhook_event(operation, table, record, old_record)

        if result:
            return JsonResponse({'status': 'success'})
        else:
            return JsonResponse({'status': 'error'}, status=500)

    except Exception as e:
        logger.error(f"Error processing category webhook: {e}")
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def subcategory_webhook(request):
    """Handle Subcategory model changes from Sequin"""
    try:
        payload = json.loads(request.body)
        operation = payload.get('operation')
        table = payload.get('table')
        record = payload.get('record', {})
        old_record = payload.get('old_record', {})

        result = handle_webhook_event(operation, table, record, old_record)

        if result:
            return JsonResponse({'status': 'success'})
        else:
            return JsonResponse({'status': 'error'}, status=500)

    except Exception as e:
        logger.error(f"Error processing subcategory webhook: {e}")
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def product_subcategory_webhook(request):
    """Handle Product-Subcategory M2M relationship changes from Sequin"""
    try:
        payload = json.loads(request.body)
        operation = payload.get('operation')
        table = payload.get('table')
        record = payload.get('record', {})
        old_record = payload.get('old_record', {})

        result = handle_webhook_event(operation, table, record, old_record)

        if result:
            return JsonResponse({'status': 'success'})
        else:
            return JsonResponse({'status': 'error'}, status=500)

    except Exception as e:
        logger.error(f"Error processing product-subcategory webhook: {e}")
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)
