from django.db import models
from django.core.files.base import ContentFile
from django.db.models.signals import post_save, post_delete, m2m_changed, pre_delete, pre_save
from django.dispatch import receiver
from authz.models.tenant import TenantModel
from products.models.category import Subcategory
from coreservice.storage_backends import CategoryImageStorage
from products.utils.image_processing import convert_to_webp, process_webp_images, cleanup_images, cleanup_old_images

def product_image_upload_path(instance, filename):
    """Generate upload path for product thumbnail images"""
    return f"Product/Thumbnail/{instance.sku}.webp"


class Product(TenantModel):
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    sku = models.CharField(max_length=100)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    brand = models.CharField(max_length=100, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    image_url = models.JSONField(default=list, blank=True, null=True)
    weight = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True)
    weight_unit = models.CharField(max_length=10, blank=True, null=True)
    subcategories = models.ManyToManyField(Subcategory, related_name='products')
    thumbnail_url = models.URLField()
    thumbnail_image = models.ImageField(
        upload_to='Product/',
        storage=CategoryImageStorage(),
        null=True,
        blank=True,
        help_text="Upload an image file. Will be converted to WebP format and stored in S3."
    )
    display_alias = models.TextField(blank=True, null=True)
    tags = models.JSONField(default=list, blank=True, null=True)
    attributes = models.JSONField(default=list, blank=True, null=True)
    colour = models.CharField(max_length=64, blank=True, null=True)

    class Meta:
        db_table = 'products'
        unique_together = ('sku', 'facility')

    def __str__(self):
        return self.name

    # def save(self, *args, **kwargs):
    #     process_image_upload(self, self.thumbnail_image, 'product')
    #     super().save(*args, **kwargs)

    def to_typesense_dict(self):
        subcategories = self.subcategories.all()
        subcategory_names = [sub.name for sub in subcategories]
        category_names = list(set([sub.category.name for sub in subcategories]))
        
        return {
            'id': str(self.id),
            'name': self.name,
            'description': self.description or '',
            'sku': self.sku,
            'price': float(self.price),
            'brand': self.brand or '',
            'is_active': self.is_active,
            'facility_id': str(self.facility.id),
            'facility_name': self.facility.name,
            'categories': category_names,
            'subcategories': subcategory_names,
            'created_at': self.creation_date.isoformat(),
            'updated_at': self.updation_date.isoformat(),
            'weight': float(self.weight) if self.weight is not None else None,
            'weight_unit': self.weight_unit or '',
            'image_url': self.image_url or [],
            'thumbnail_url': self.thumbnail_url or '',
            'display_alias': self.display_alias or [],
            'tags': self.tags or [],
            'colour': self.colour or '',
        }


@receiver(post_save, sender=Product)
def process_product_images_async(sender, instance, created, **kwargs):
    if instance.thumbnail_image and hasattr(instance.thumbnail_image, 'name'):
        process_webp_images(instance, 'Product', [(200, 200), (300, 300)])


@receiver(post_save, sender=Product)
def update_typesense_on_save(sender, instance, created, **kwargs):
    from products.typesense.client import get_typesense_client
    if hasattr(instance, '_typesense_update_complete') and instance._typesense_update_complete:
        return
    try:
        client = get_typesense_client()
        document = instance.to_typesense_dict()
        client.collections['products'].documents.upsert(document)
        print(f"Product {instance.id} indexed in Typesense")
    except Exception as e:
        print(f"Error indexing product {instance.id} in Typesense: {e}")


@receiver(post_delete, sender=Product)
def delete_from_typesense_on_delete(sender, instance, **kwargs):
    from products.typesense.client import get_typesense_client
    try:
        client = get_typesense_client()
        client.collections['products'].documents[str(instance.id)].delete()
        print(f"Product {instance.id} deleted from Typesense")
    except Exception as e:
        print(f"Error deleting product {instance.id} from Typesense: {e}")


@receiver(m2m_changed, sender=Product.subcategories.through)
def update_typesense_on_m2m_change(sender, instance, action, **kwargs):
    if action not in ('post_add', 'post_remove', 'post_clear'):
        return
    try:
        instance._typesense_update_complete = True
        update_typesense_on_save(sender=Product, instance=instance, created=False)
    except Exception as e:
        print(f"Typesense update error: {e}")



@receiver(pre_delete, sender=Product)
def delete_product_image_on_delete(sender, instance, **kwargs):
    if instance.thumbnail_image:
        try:
            instance.thumbnail_image.delete(save=False)
            print(f"Deleted image for product {instance.sku}: {instance.thumbnail_image.name}")
        except Exception as e:
            print(f"Warning: Could not delete image for product {instance.sku}: {e}")
    cleanup_images(instance, 'Product')


@receiver(pre_save, sender=Product)
def delete_old_product_image_on_change(sender, instance, **kwargs):
    if not instance.pk:
        return
    try:
        old_instance = Product.objects.get(pk=instance.pk)
    except Product.DoesNotExist:
        return
    cleanup_old_images(old_instance, instance, 'Product')
