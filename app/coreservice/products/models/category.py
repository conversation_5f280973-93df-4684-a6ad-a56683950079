from django.db import models
from django.db.models.signals import post_save, post_delete, pre_delete, pre_save
from django.dispatch import receiver
from authz.models.tenant import BaseModel
from coreservice.storage_backends import CategoryImageStorage
from products.utils.image_processing import process_webp_images, cleanup_images, cleanup_old_images


class Category(BaseModel):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    code = models.Char<PERSON>ield(max_length=50, unique=True)
    is_active = models.BooleanField(default=True)
    thumbnail_url = models.URLField()
    thumbnail_image = models.ImageField(
        upload_to="Category/",
        storage=CategoryImageStorage(),
        null=True,
        blank=True,
        help_text="Upload image file. Original stored with filename, WebP versions generated automatically."
    )
    section = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        db_table = 'product_categories'
        ordering = ['name']
        verbose_name_plural = 'Categories'
    
    def __str__(self):
        return self.name

    def to_typesense_dict(self):
        return {
            'id': str(self.id),
            'name': self.name,
            'description': self.description,
            'code': self.code,
            'is_active': self.is_active,
            'section': str(self.section),
            'thumbnail_url': self.thumbnail_url,
        }

@receiver(pre_delete, sender=Category)
def delete_category_image_on_delete(sender, instance, **kwargs):
    if instance.thumbnail_image:
        try:
            instance.thumbnail_image.delete(save=False)
        except Exception as e:
            print(f"Could not delete image for category {instance.code}: {e}")
    cleanup_images(instance, 'Category')

@receiver(pre_save, sender=Category)
def delete_old_category_image_on_change(sender, instance, **kwargs):
    if not instance.pk:
        return
    try:
        old_instance = Category.objects.get(pk=instance.pk)
    except Category.DoesNotExist:
        return
    cleanup_old_images(old_instance, instance, 'Category')


@receiver(post_save, sender=Category)
def process_category_images_async(sender, instance, created, **kwargs):
    if instance.thumbnail_image and hasattr(instance.thumbnail_image, 'name'):
        process_webp_images(instance, 'Category', [(200, 200), (300, 300)])


class Subcategory(BaseModel):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    code = models.CharField(max_length=50, unique=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='subcategories')
    is_active = models.BooleanField(default=True)
    thumbnail_url = models.CharField(max_length=512)
    thumbnail_image = models.ImageField(
        upload_to='Subcategory/',
        storage=CategoryImageStorage(),
        null=True,
        blank=True,
        help_text="Upload image file. Original stored with filename, WebP versions generated automatically."
    )

    class Meta:
        db_table = 'product_subcategories'
        ordering = ['category__name', 'name']
        verbose_name_plural = 'Subcategories'
    
    def __str__(self):
        return f"{self.category.name} - {self.name}"

    def to_typesense_dict(self):
        return {
            'id': str(self.id),
            'name': self.name,
            'description': self.description,
            'code': self.code,
            'category_id': self.category.id,
            'category_name': self.category.name,
            'is_active': self.is_active,
            'thumbnail_url': self.thumbnail_url,
        }


def _upsert_to_typesense(instance, collection_name, entity_type):
    from products.typesense.client import get_typesense_client

    try:
        client = get_typesense_client()
        document = instance.to_typesense_dict()
        client.collections[collection_name].documents.upsert(document)
        print(f"{entity_type} {instance.id} indexed in Typesense")
        return True
    except Exception as e:
        print(f"Error indexing {entity_type.lower()} {instance.id} in Typesense: {e}")
        return False


def _delete_from_typesense(instance, collection_name, entity_type):
    from products.typesense.client import get_typesense_client

    try:
        client = get_typesense_client()
        client.collections[collection_name].documents[str(instance.id)].delete()
        print(f"{entity_type} {instance.id} deleted from Typesense")
        return True
    except Exception as e:
        print(f"Error deleting {entity_type.lower()} {instance.id} from Typesense: {e}")
        return False


def _bulk_update_typesense(objects, collection_name, entity_type, reason=""):
    from products.typesense.client import get_typesense_client

    if not objects.exists():
        return

    try:
        client = get_typesense_client()
        success_count = 0
        error_count = 0

        for obj in objects:
            try:
                document = obj.to_typesense_dict()
                client.collections[collection_name].documents.upsert(document)
                success_count += 1
            except Exception as e:
                error_count += 1
                print(f"Error updating {entity_type.lower()} {obj.id} in Typesense: {e}")

        if success_count > 0:
            print(f"Updated {success_count} {entity_type.lower()}(s) in Typesense{reason}")
        if error_count > 0:
            print(f"Failed to update {error_count} {entity_type.lower()}(s) in Typesense{reason}")

    except Exception as e:
        print(f"Error bulk updating {entity_type.lower()}s in Typesense{reason}: {e}")


@receiver(post_save, sender=Category)
def update_category_typesense_on_save(sender, instance, created, **kwargs):
    if _upsert_to_typesense(instance, 'categories', 'Category') and not created:
        subcategories = instance.subcategories.all()
        _bulk_update_typesense(subcategories, 'subcategories', 'Subcategory',
                              f" due to category {instance.id} change")
        from products.models.product import Product
        products = Product.objects.filter(subcategories__in=subcategories).distinct()
        _bulk_update_typesense(products, 'products', 'Product',
                              f" due to category {instance.id} change")


@receiver(post_delete, sender=Category)
def delete_category_from_typesense_on_delete(sender, instance, **kwargs):
    _delete_from_typesense(instance, 'categories', 'Category')


@receiver(post_save, sender=Subcategory)
def update_subcategory_typesense_on_save(sender, instance, created, **kwargs):
    if _upsert_to_typesense(instance, 'subcategories', 'Subcategory') and not created:
        products = instance.products.all()
        _bulk_update_typesense(products, 'products', 'Product',
                              f" due to subcategory {instance.id} change")



@receiver(post_save, sender=Subcategory)
def process_subcategory_images_async(sender, instance, created, **kwargs):
    if instance.thumbnail_image and hasattr(instance.thumbnail_image, 'name'):
        process_webp_images(instance, 'Subcategory', [(200, 200), (300, 300)])


@receiver(pre_delete, sender=Subcategory)
def delete_subcategory_image_on_delete(sender, instance, **kwargs):
    if instance.thumbnail_image:
        try:
            instance.thumbnail_image.delete(save=False)
        except Exception as e:
            print(f"Could not delete image for subcategory {instance.code}: {e}")
    cleanup_images(instance, 'Subcategory')

@receiver(pre_save, sender=Subcategory)
def delete_old_subcategory_image_on_change(sender, instance, **kwargs):
    if not instance.pk:
        return
    try:
        old_instance = Subcategory.objects.get(pk=instance.pk)
    except Subcategory.DoesNotExist:
        return
    cleanup_old_images(old_instance, instance, 'Subcategory')
