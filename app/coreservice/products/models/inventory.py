from authz.models.tenant import TenantModel
from products.models.product import Product


class ProductInventory(TenantModel):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='inventory')
    total_quantity = models.IntegerField(default=0)
    tolerance_quantity = models.IntegerField(default=0)
    available_quantity = models.IntegerField(default=0)
    
    def __str__(self):
        return f"{self.product.name} - {self.total_quantity}"