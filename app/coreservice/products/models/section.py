import uuid
from django.db import models
from django.db.models.signals import post_save, post_delete, m2m_changed
from django.dispatch import receiver
from authz.models.tenant import BaseModel
from products.models import Product
from django.utils import timezone
from datetime import timedelta
from products.typesense.client import get_typesense_client


class Section(BaseModel):

    SECTION_TYPE_CHOICES = [
        ('CURATED', 'Curated'),
        ('CATEGORY_BASED', 'Category Based'),
        ('TAG_BASED', 'Tag Based'),
        ('BRAND_BASED', 'Brand Based'),
        ('ALGORITHMIC', 'Algorithmic'),
        ('PROMOTIONAL', 'Promotional'),
    ]

    SECTION_CONTEXT_CHOICES = [
        ('HOMEPAGE', 'Homepage'),
        ('CATEGORY_PAGE', 'Category Page'),
        ('PRODUCT_PAGE', 'Product Page'),
        ('CART_PAGE', 'Cart Page'),
        ('FOOTER', 'Footer'),
        ('NAVIGATION', 'Navigation'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    slug = models.SlugField(max_length=255,unique=True,blank=True)
    section_type = models.CharField(max_length=20, choices=SECTION_TYPE_CHOICES)
    context = models.CharField(max_length=20, choices=SECTION_CONTEXT_CHOICES, default='HOMEPAGE')
    priority = models.PositiveIntegerField()
    display_limit = models.PositiveIntegerField(default=10)
    is_active = models.BooleanField(default=True)
    start_date = models.DateTimeField(blank=True, null=True)
    end_date = models.DateTimeField(blank=True, null=True)
    
    products = models.ManyToManyField('products.Product', blank=True, related_name='sections')
    categories = models.ManyToManyField('products.Category', blank=True, related_name='sections')
    subcategories = models.ManyToManyField('products.Subcategory', blank=True, related_name='sections')
    tags = models.JSONField(default=list, blank=True)
    brands = models.JSONField(default=list, blank=True)

    background_color = models.CharField(max_length=7, blank=True, null=True)
    text_color = models.CharField(max_length=7, blank=True, null=True,)
    metadata = models.JSONField(default=dict, blank=True)
    
    class Meta:
        ordering = ['priority', 'name']
        verbose_name = 'Homepage Section'
        verbose_name_plural = 'Homepage Sections'
        
    def __str__(self):
        return f"{self.name} ({self.get_section_type_display()})"

    def to_typesense_dict(self):
        import json

        try:
            products = self.products.all()
            categories = self.categories.all()
            subcategories = self.subcategories.all()
        except Exception:
            products = self.products.none()
            categories = self.categories.none()
            subcategories = self.subcategories.none()

        product_names = [product.name for product in products]
        category_names = [category.name for category in categories]
        sub_category_names = [sub_category.name for sub_category in subcategories]

        return {
            'id': str(self.id),
            'name': self.name,
            'slug': self.slug or '',
            'section_type': self.section_type,
            'context': self.context,
            'priority': self.priority,
            'display_limit': self.display_limit,
            'tags': self.tags or [],
            'brands': self.brands or [],
            'background_color': self.background_color or '',
            'text_color': self.text_color or '',
            'metadata': json.dumps(self.metadata or {}),  # Convert to JSON string
            'is_active': self.is_active,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'created_at': self.creation_date.isoformat(),
            'updated_at': self.updation_date.isoformat(),
            'product_count': len(product_names),
            'category_count': len(category_names),
            'subcategory_count': len(sub_category_names),
            'product_names': product_names,
            'category_names': category_names,
            'subcategory_names': sub_category_names,
        }
    
    def save(self, *args, **kwargs):
        if not self.slug:
            from django.utils.text import slugify
            self.slug = slugify(self.name)

        if not self.priority or self.priority == 1:
            max_priority = Section.objects.aggregate(
                max_priority=models.Max('priority')
            )['max_priority']
            self.priority = (max_priority or 0) + 1

        super().save(*args, **kwargs)


@receiver(post_save, sender=Section)
def update_section_typesense_on_save(sender, instance, created, **kwargs):
    if getattr(instance, '_typesense_update_complete', False):
        return
    try:
        client = get_typesense_client()
        document = instance.to_typesense_dict()
        client.collections['sections'].documents.upsert(document)
        print(f"Section {instance.id} indexed in Typesense")
    except Exception as e:
        print(f"Error indexing section {instance.id} in Typesense: {e}")
    finally:
        # Always cleanup the flag to avoid stale state
        if hasattr(instance, '_typesense_update_complete'):
            delattr(instance, '_typesense_update_complete')


@receiver(post_delete, sender=Section)
def delete_section_from_typesense_on_delete(sender, instance, **kwargs):
    try:
        client = get_typesense_client()
        client.collections['sections'].documents[str(instance.id)].delete()
        print(f"Section {instance.id} deleted from Typesense")
    except Exception as e:
        if "404" in str(e) or "Could not find a document" in str(e):
            print(f"Section {instance.id} was not found in Typesense (may not have been indexed)")
        else:
            print(f"Error deleting section {instance.id} from Typesense: {e}")


@receiver(m2m_changed, sender=Section.products.through)
def update_section_typesense_on_products_change(sender, instance, action, **kwargs):
    if action not in ('post_add', 'post_remove', 'post_clear'):
        return

    if hasattr(instance, '_skip_typesense_update') and instance._skip_typesense_update:
        return

    try:
        instance._typesense_update_complete = True
        update_section_typesense_on_save(sender=Section, instance=instance, created=False)
    except Exception as e:
        print(f"Section Typesense update error: {e}")


@receiver(m2m_changed, sender=Section.categories.through)
def update_section_typesense_on_categories_change(sender, instance, action, **kwargs):
    if action not in ('post_add', 'post_remove', 'post_clear'):
        return

    if hasattr(instance, '_skip_typesense_update') and instance._skip_typesense_update:
        return

    try:
        instance._typesense_update_complete = True
        update_section_typesense_on_save(sender=Section, instance=instance, created=False)
    except Exception as e:
        print(f"Section Typesense update error: {e}")


@receiver(m2m_changed, sender=Section.subcategories.through)
def update_section_typesense_on_subcategories_change(sender, instance, action, **kwargs):
    if action not in ('post_add', 'post_remove', 'post_clear'):
        return
    if getattr(instance, '_skip_typesense_update', False):
        return
    try:
        instance._typesense_update_complete = True
        update_section_typesense_on_save(sender=Section, instance=instance, created=False)
    except Exception as e:
        print(f"Section Typesense update error (subcategories): {e}")
