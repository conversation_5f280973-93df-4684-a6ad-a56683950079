from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from products.views import (
    ProductViewSet, CategoryViewSet, SubcategoryViewSet, SectionViewSet,
    ecommerce_demo, homepage_sections, test_endpoint
)

router = DefaultRouter()
router.register(r'products', ProductViewSet)
router.register(r'categories', CategoryViewSet)
router.register(r'subcategories', SubcategoryViewSet)
router.register(r'sections', SectionViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('demo/', ecommerce_demo, name='ecommerce_demo'),
    path('homepage-sections/', homepage_sections, name='homepage_sections'),
    path('test/', test_endpoint, name='test_endpoint'),
]
