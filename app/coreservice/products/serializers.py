from rest_framework import serializers
from products.models import Product, Category, Subcategory, Section


class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = '__all__'
        read_only_fields = ('id', 'creation_date', 'updation_date')


class SubcategorySerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    
    class Meta:
        model = Subcategory
        fields = '__all__'
        read_only_fields = ('id', 'creation_date', 'updation_date')


class ProductSerializer(serializers.ModelSerializer):
    facility_name = serializers.CharField(source='facility.name', read_only=True)
    subcategory_details = SubcategorySerializer(source='subcategories', many=True, read_only=True)
    
    class Meta:
        model = Product
        fields = '__all__'
        read_only_fields = ('id', 'creation_date', 'updation_date')


class ProductSearchSerializer(serializers.Serializer):
    query = serializers.CharField(required=False, allow_blank=True)
    category = serializers.CharField(required=False, allow_blank=True)
    subcategory = serializers.CharField(required=False, allow_blank=True)
    facility_id = serializers.CharField(required=False, allow_blank=True)
    brand = serializers.CharField(required=False, allow_blank=True)
    min_price = serializers.DecimalField(required=False, max_digits=10, decimal_places=2)
    max_price = serializers.DecimalField(required=False, max_digits=10, decimal_places=2)
    sort_by = serializers.CharField(required=False, default='name')
    sort_order = serializers.ChoiceField(choices=['asc', 'desc'], default='asc', required=False)


class SectionSerializer(serializers.ModelSerializer):
    """
    Serializer for Section model with related data
    """
    product_count = serializers.IntegerField(read_only=True)
    category_count = serializers.IntegerField(read_only=True)
    subcategory_count = serializers.IntegerField(read_only=True)

    class Meta:
        model = Section
        fields = [
            'id', 'name', 'slug', 'section_type', 'context', 'priority',
            'display_limit', 'tags', 'brands', 'background_color', 'text_color',
            'metadata', 'is_active', 'start_date', 'end_date', 'creation_date',
            'updation_date', 'product_count', 'category_count', 'subcategory_count'
        ]
        read_only_fields = ('id', 'creation_date', 'updation_date')


class SectionSearchSerializer(serializers.Serializer):
    """
    Serializer for section search parameters
    """
    query = serializers.CharField(required=False, allow_blank=True)
    section_type = serializers.CharField(required=False, allow_blank=True)
    context = serializers.CharField(required=False, allow_blank=True)
    tags = serializers.CharField(required=False, allow_blank=True)
    brands = serializers.CharField(required=False, allow_blank=True)
    is_active = serializers.BooleanField(required=False)
    sort_by = serializers.CharField(required=False, default='priority')
    sort_order = serializers.ChoiceField(choices=['asc', 'desc'], default='asc', required=False)
    per_page = serializers.IntegerField(required=False, default=20, min_value=1, max_value=100)
