from django.core.management.base import BaseCommand
from django.db import transaction
from products.models import Section, Category, Subcategory, Product
import random

class Command(BaseCommand):
    help = 'Create sample sections using existing categories, products, and brands'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing sections before creating new ones',
        )

    def handle(self, *args, **options):
        clear_existing = options.get('clear', False)

        try:
            if clear_existing:
                self._clear_existing_sections()

            self.stdout.write('Creating sample sections...')

            with transaction.atomic():
                self._create_sample_sections()

            self.stdout.write(self.style.SUCCESS('Sample sections created successfully!'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error creating sections: {e}'))
            raise

    def _create_sample_sections(self):
        """Create diverse sample sections for e-commerce"""

        # Get available data for relationships
        categories = list(Category.objects.filter(is_active=True))
        products = list(Product.objects.filter(is_active=True))

        if not categories:
            self.stdout.write(self.style.WARNING('No categories found. Run generate_sample_data first.'))
            return

        if not products:
            self.stdout.write(self.style.WARNING('No products found. Run generate_sample_data first.'))
            return

        # Get unique brands from products
        brands = list(set(product.brand for product in products if product.brand))

        sections_data = [
            # CURATED sections
            {
                'name': 'Fresh Fruits & Vegetables',
                'section_type': 'CURATED',
                'context': 'HOMEPAGE',
                'priority': 1,
                'display_limit': 12,
                'background_color': '#4CAF50',
                'text_color': '#FFFFFF',
                'metadata': {'featured': True, 'description': 'Farm fresh produce delivered daily'},
            },
            {
                'name': 'Daily Dose of Nutrition',
                'section_type': 'CURATED',
                'context': 'HOMEPAGE',
                'priority': 2,
                'display_limit': 8,
                'background_color': '#FF9800',
                'text_color': '#FFFFFF',
                'metadata': {'health_focused': True},
            },

            # ALGORITHMIC sections
            {
                'name': 'Buy Again',
                'section_type': 'ALGORITHMIC',
                'context': 'HOMEPAGE',
                'priority': 3,
                'display_limit': 10,
                'metadata': {'algorithm_type': 'buy_again', 'user_specific': True},
            },
            {
                'name': 'Trending Now',
                'section_type': 'ALGORITHMIC',
                'context': 'HOMEPAGE',
                'priority': 4,
                'display_limit': 15,
                'background_color': '#E91E63',
                'text_color': '#FFFFFF',
                'metadata': {'algorithm_type': 'trending', 'trending_days': 7},
            },
            {
                'name': 'Most Sold Products',
                'section_type': 'ALGORITHMIC',
                'context': 'HOMEPAGE',
                'priority': 5,
                'display_limit': 12,
                'metadata': {'algorithm_type': 'most_sold', 'time_period': '30_days'},
            },

            # PROMOTIONAL sections
            {
                'name': 'Big Pack, Big Savings',
                'section_type': 'PROMOTIONAL',
                'context': 'HOMEPAGE',
                'priority': 6,
                'display_limit': 8,
                'background_color': '#FF5722',
                'text_color': '#FFFFFF',
                'metadata': {'promo_type': 'bulk_discount', 'discount_min': 25},
                'tags': ['bulk', 'family-pack', 'savings'],
            },
            {
                'name': 'Deal of the Day',
                'section_type': 'PROMOTIONAL',
                'context': 'HOMEPAGE',
                'priority': 7,
                'display_limit': 6,
                'background_color': '#9C27B0',
                'text_color': '#FFFFFF',
                'metadata': {'promo_type': 'daily_deal', 'discount_min': 30, 'time_limited': True},
                'tags': ['deal', 'limited-time'],
            },

            # CATEGORY_BASED sections
            {
                'name': 'Explore All Categories',
                'section_type': 'CATEGORY_BASED',
                'context': 'HOMEPAGE',
                'priority': 8,
                'display_limit': 20,
                'metadata': {'show_all_categories': True, 'grid_layout': True},
            },
            {
                'name': 'Sweet Treats & Snacks',
                'section_type': 'CATEGORY_BASED',
                'context': 'HOMEPAGE',
                'priority': 9,
                'display_limit': 10,
                'background_color': '#795548',
                'text_color': '#FFFFFF',
                'metadata': {'category_focus': 'snacks_sweets'},
            },

            # TAG_BASED sections
            {
                'name': 'Organic & Natural',
                'section_type': 'TAG_BASED',
                'context': 'HOMEPAGE',
                'priority': 10,
                'display_limit': 12,
                'background_color': '#8BC34A',
                'text_color': '#FFFFFF',
                'tags': ['organic', 'natural', 'healthy', 'eco-friendly'],
                'metadata': {'health_category': True},
            },
            {
                'name': 'Quick & Easy Meals',
                'section_type': 'TAG_BASED',
                'context': 'HOMEPAGE',
                'priority': 11,
                'display_limit': 8,
                'tags': ['ready-to-eat', 'instant', 'quick-cook', 'convenience'],
                'metadata': {'convenience_focused': True},
            },

            # BRAND_BASED sections
            {
                'name': 'Premium Brands',
                'section_type': 'BRAND_BASED',
                'context': 'HOMEPAGE',
                'priority': 12,
                'display_limit': 15,
                'background_color': '#607D8B',
                'text_color': '#FFFFFF',
                'brands': brands[:5] if len(brands) >= 5 else brands,  # Top 5 brands
                'metadata': {'premium_selection': True, 'curated_brands': True},
            },
            {
                'name': 'Local Favorites',
                'section_type': 'BRAND_BASED',
                'context': 'HOMEPAGE',
                'priority': 13,
                'display_limit': 10,
                'brands': brands[5:10] if len(brands) >= 10 else brands[len(brands)//2:],
                'metadata': {'local_brands': True},
            },

            # Context-specific sections
            {
                'name': 'You May Also Like',
                'section_type': 'ALGORITHMIC',
                'context': 'PRODUCT_PAGE',
                'priority': 1,
                'display_limit': 6,
                'metadata': {'algorithm_type': 'similar_products', 'recommendation_type': 'collaborative'},
            },
            {
                'name': 'Complete Your Cart',
                'section_type': 'ALGORITHMIC',
                'context': 'CART_PAGE',
                'priority': 1,
                'display_limit': 4,
                'metadata': {'algorithm_type': 'cart_completion', 'frequently_bought_together': True},
            },
            {
                'name': 'Quick Links',
                'section_type': 'CATEGORY_BASED',
                'context': 'FOOTER',
                'priority': 100,
                'display_limit': 8,
                'metadata': {'footer_navigation': True},
            }
        ]

        created_count = 0
        updated_count = 0

        for section_data in sections_data:
            # Extract relationship data before creating section
            categories_to_add = section_data.pop('categories', [])
            products_to_add = section_data.pop('products', [])

            section, created = Section.objects.get_or_create(
                name=section_data['name'],
                defaults=section_data
            )

            if created:
                created_count += 1
                self.stdout.write(f'✅ Created section: {section.name}')
            else:
                # Update existing section with new data
                for key, value in section_data.items():
                    if key != 'name':  # Don't update the name
                        setattr(section, key, value)
                section.save()
                updated_count += 1
                self.stdout.write(f'🔄 Updated section: {section.name}')

            # Assign relationships
            self._assign_section_relationships(section, categories_to_add, products_to_add, categories, products, brands)

        self.stdout.write(self.style.SUCCESS(
            f'Successfully processed {created_count} new sections and updated {updated_count} existing sections'
        ))

    def _assign_section_relationships(self, section, categories_to_add, products_to_add, all_categories, all_products, all_brands):
        """Assign categories, products, and other relationships to sections based on section type"""

        if section.section_type == 'CURATED':
            # For curated sections, assign some random products
            if not products_to_add:
                sample_products = random.sample(all_products, min(section.display_limit, len(all_products)))
                section.products.set(sample_products)

        elif section.section_type == 'CATEGORY_BASED':
            if categories_to_add:
                section.categories.set(categories_to_add)
            elif 'Explore All' in section.name or 'Quick Links' in section.name:
                # Assign all categories for navigation sections
                section.categories.set(all_categories)
            else:
                # Assign relevant categories based on section name
                relevant_categories = self._find_relevant_categories(section.name, all_categories)
                if relevant_categories:
                    section.categories.set(relevant_categories)

        elif section.section_type == 'BRAND_BASED':
            # Brands are already set in the section data via the brands JSONField
            if not section.brands and all_brands:
                # Assign some brands if none specified
                section.brands = random.sample(all_brands, min(3, len(all_brands)))
                section.save()

        elif section.section_type == 'TAG_BASED':
            # Tags are already set in the section data via the tags JSONField
            pass

        elif section.section_type in ['ALGORITHMIC', 'PROMOTIONAL']:
            # These sections get their products dynamically via the get_products() method
            # But we can assign some sample products for testing
            if not products_to_add:
                sample_products = random.sample(all_products, min(section.display_limit // 2, len(all_products)))
                section.products.set(sample_products)

    def _find_relevant_categories(self, section_name, categories):
        """Find categories relevant to the section name"""
        relevant = []
        name_lower = section_name.lower()

        # Define keyword mappings
        keyword_mappings = {
            'sweet': ['chocolate', 'candy', 'dessert', 'sweet'],
            'drink': ['drink', 'juice', 'beverage', 'water'],
            'snack': ['snack', 'chip', 'namkeen', 'biscuit'],
            'fresh': ['fruit', 'vegetable', 'fresh'],
            'home': ['cleaning', 'household', 'home'],
            'beauty': ['beauty', 'personal', 'care', 'cosmetic'],
        }

        for keyword, category_keywords in keyword_mappings.items():
            if keyword in name_lower:
                for category in categories:
                    if any(cat_keyword in category.name.lower() for cat_keyword in category_keywords):
                        relevant.append(category)
                break

        return relevant[:3]  # Limit to 3 categories

    def _clear_existing_sections(self):
        """Clear all existing sections"""
        count = Section.objects.count()
        if count > 0:
            Section.objects.all().delete()
            self.stdout.write(f'🗑️  Cleared {count} existing sections')