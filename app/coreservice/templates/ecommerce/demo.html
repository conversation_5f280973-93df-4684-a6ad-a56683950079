<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rozana - E-commerce Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: bold;
        }
        
        .search-bar {
            flex: 1;
            max-width: 400px;
            margin: 0 2rem;
        }
        
        .search-bar input {
            width: 100%;
            padding: 0.75rem;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            font-size: 1.2rem;
            color: #666;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
        }
        
        .section {
            background: white;
            margin: 2rem 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
        }
        
        .section-meta {
            font-size: 0.9rem;
            color: #666;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            padding: 1.5rem;
        }
        
        .product-card {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            transition: transform 0.2s, box-shadow 0.2s;
            background: white;
        }
        
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .product-image {
            width: 100%;
            height: 120px;
            background: #f8f9fa;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.5rem;
            font-size: 3rem;
            color: #ccc;
        }
        
        .product-name {
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            line-height: 1.3;
        }
        
        .product-price {
            color: #28a745;
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .product-brand {
            color: #666;
            font-size: 0.8rem;
            margin-bottom: 0.5rem;
        }
        
        .no-products {
            text-align: center;
            padding: 2rem;
            color: #666;
            font-style: italic;
        }
        
        .stats {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 1rem;
            text-align: center;
            margin: 2rem 0;
            border-radius: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 3rem;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .search-bar {
                margin: 0;
                max-width: 100%;
            }
            
            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">🛒 Rozana</div>
                <div class="search-bar">
                    <input type="text" placeholder="Search products..." id="searchInput">
                </div>
                <div>E-commerce Demo</div>
            </div>
        </div>
    </header>

    <main class="container">
        <div class="stats">
            <h2>📊 Store Statistics</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number" id="totalSections">-</span>
                    <span class="stat-label">Active Sections</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="totalProducts">-</span>
                    <span class="stat-label">Total Products</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="avgProductsPerSection">-</span>
                    <span class="stat-label">Avg Products/Section</span>
                </div>
            </div>
        </div>

        <div id="loading" class="loading">
            🔄 Loading sections and products...
        </div>
        
        <div id="error" class="error" style="display: none;">
            ❌ <span id="errorMessage"></span>
        </div>
        
        <div id="sectionsContainer">
            <!-- Sections will be loaded here -->
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 Rozana E-commerce Demo. Powered by Django + Typesense</p>
        </div>
    </footer>

    <script>
        // API endpoints
        const API_BASE = '/api/products';
        
        // Load homepage sections on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadHomepageSections();
        });
        
        async function loadHomepageSections() {
            try {
                showLoading(true);
                hideError();

                const response = await fetch(`${API_BASE}/homepage-sections/`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                displaySections(data.sections || []);
                updateStats(data.sections || []);

            } catch (error) {
                console.error('Error loading sections:', error);
                showError(`Failed to load sections: ${error.message}`);
            } finally {
                showLoading(false);
            }
        }
        
        function displaySections(sections) {
            const container = document.getElementById('sectionsContainer');
            
            if (!sections.length) {
                container.innerHTML = '<div class="no-products">No sections found</div>';
                return;
            }
            
            container.innerHTML = sections.map(section => `
                <div class="section">
                    <div class="section-header">
                        <div>
                            <h2 class="section-title">${section.name}</h2>
                            <div class="section-meta">
                                ${section.section_type} • ${section.context} • Priority: ${section.priority}
                            </div>
                        </div>
                        <div class="section-meta">
                            ${section.products?.length || 0} products
                        </div>
                    </div>
                    <div class="products-grid">
                        ${displayProducts(section.products || [])}
                    </div>
                </div>
            `).join('');
        }
        
        function displayProducts(products) {
            if (!products.length) {
                return '<div class="no-products">No products in this section</div>';
            }

            return products.map(product => `
                <div class="product-card">
                    <div class="product-image">📦</div>
                    <div class="product-name">${product.name || 'Unknown Product'}</div>
                    ${product.brand ? `<div class="product-brand">${product.brand}</div>` : ''}
                    <div class="product-price">₹${product.price || '0.00'}</div>
                </div>
            `).join('');
        }
        
        function updateStats(sections) {
            const totalSections = sections.length;
            const totalProducts = sections.reduce((sum, section) => sum + (section.products?.length || 0), 0);
            const avgProducts = totalSections > 0 ? Math.round(totalProducts / totalSections) : 0;
            
            document.getElementById('totalSections').textContent = totalSections;
            document.getElementById('totalProducts').textContent = totalProducts;
            document.getElementById('avgProductsPerSection').textContent = avgProducts;
        }
        
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
        
        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('error').style.display = 'block';
        }
        
        function hideError() {
            document.getElementById('error').style.display = 'none';
        }
        
        // Search functionality (placeholder)
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const query = this.value.trim();
                if (query) {
                    alert(`Search functionality would search for: "${query}"`);
                }
            }
        });
    </script>
</body>
</html>
