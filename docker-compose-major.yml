version: '3.8'

services:
  db1:
    image: postgres:15
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=rozana
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
      
  typesense:
    image: typesense/typesense:0.25.1
    container_name: typesense
    restart: on-failure
    ports:
      - "8108:8108"
    volumes:
      - typesense_data:/data
    environment:
      - TYPESENSE_API_KEY=xyz
      - TYPESENSE_DATA_DIR=/data
    command: --data-dir /data --api-key=xyz --enable-cors    
      
  web:
    container_name: core_service
    build:
      context: ./app
    volumes:
      - ./app/coreservice:/application
    ports:
      - "8081:8000"
    env_file:
      - .env
    depends_on:
      db1:
        condition: service_healthy
      typesense:
        condition: service_started
    environment:
      - TYPESENSE_API_KEY=xyz
      - TYPESENSE_HOST=typesense
      - TYPESENSE_PORT=8108
      - TYPESENSE_PROTOCOL=http
    stdin_open: true
    tty: true
    command: python manage.py runserver 0.0.0.0:8000

volumes:
  postgres_data:
  typesense_data:
