# ROZANA-CORE Service

ROZANA-CORE is a Django-based Order Management System (OMS) designed to handle order processing, product management, and facility management with TypeSense integration for fast product search capabilities.

## Project Overview

This service provides a comprehensive API for:
- Order management (creation, tracking, and fulfillment)
- Product catalog management with TypeSense search integration
- User authentication and authorization
- Facility and company management

## Tech Stack

- **Backend**: Django REST Framework
- **Database**: PostgreSQL
- **Search Engine**: TypeSense
- **Authentication**: JWT (JSON Web Tokens)
- **Containerization**: Docker and Docker Compose

## Getting Started

### Prerequisites

- Docker and Docker Compose
- Python 3.8+
- Git

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/arjunexplore29/rozana-core-service.git
   cd rozana-core-service
   ```

2. Create a `.env` file with the following environment variables:
   ```
   TYPESENSE_API_KEY=xyz
   TYPESENSE_HOST=typesense
   TYPESENSE_PORT=8108
   TYPESENSE_PROTOCOL=http
   ```

3. Start the services using Docker Compose:
   ```bash
   <!-- docker-compose up -d -->
   docker-compose -f docker-compose.yml up --build -d
   ```

4. The API will be available at `http://localhost:8000/`

## API Endpoints

### Orders API

- `POST /api/orders/` - Create a new order
- `GET /api/orders/` - List all orders
- `GET /api/orders/{order_id}/` - Get order details
- `PUT /api/orders/{order_id}/` - Update an order
- `PATCH /api/orders/{order_id}/status/` - Update order status

### Products API

- `GET /api/products/` - List all products
- `GET /api/products/{product_id}/` - Get product details
- `POST /api/products/` - Create a new product
- `PUT /api/products/{product_id}/` - Update a product
- `DELETE /api/products/{product_id}/` - Delete a product

## TypeSense Integration

The project uses TypeSense for fast and typo-tolerant product search capabilities.

### TypeSense Configuration

TypeSense is configured in the `products/typesense/client.py` file. The client is initialized with settings from the Django settings module:

```python
def get_typesense_client():
    client = typesense.Client({
        'api_key': settings.TYPESENSE_API_KEY,
        'nodes': [{
            'host': settings.TYPESENSE_HOST,
            'port': settings.TYPESENSE_PORT,
            'protocol': settings.TYPESENSE_PROTOCOL
        }],
        'connection_timeout_seconds': 5,
        'retry_interval_seconds': 2,
        'num_retries': 3
    })
    return client
```

### Product Schema in TypeSense

The product schema in TypeSense includes the following fields:

- `id`: Product ID (string)
- `name`: Product name (string)
- `description`: Product description (string, optional)
- `sku`: Product SKU (string)
- `price`: Product price (float)
- `brand`: Product brand (string, optional, facet)
- `is_active`: Product status (boolean)
- `facility_id`: Facility ID (string, facet)
- `facility_name`: Facility name (string, facet)
- `categories`: Product categories (string array, facet)
- `subcategories`: Product subcategories (string array, facet)
- `weight`: Product weight (float, optional)
- `weight_unit`: Weight unit (string, optional)
- `image_url`: Product image URL (string, optional)

## Management Commands

### 1. Generate Sample Data

This command generates sample product data from a CSV file and indexes it in TypeSense.

```bash
python manage.py generate_sample_data [--csv-file CSV_FILE] [--limit LIMIT] [--force]
```

Options:
- `--csv-file`: Path to the CSV file with product data (default: 'blinkit_products_data.csv')
- `--limit`: Limit the number of products to import (default: 200, 0 for all)
- `--force`: Force recreation of data even if it already exists

Example:
```bash
docker exec -it core_service bash
python manage.py generate_sample_data --limit 0 --force
```

### 2. Index Products in TypeSense

This command indexes all products in the database to TypeSense.

```bash
python manage.py index_typesense [--force]
```

Options:
- `--force`: Force reindexing of all products even if they are already indexed

Example:
```bash
python manage.py index_typesense --force
```

### 3. Create Admin User

This command creates an admin user for the application.

```bash
python manage.py createadmin
```

## Type Hints in the Project

The project uses Python type hints to improve code readability and enable better IDE support. Here are some examples of how type hints are used:

### Example 1: Model Type Hints

```python
from django.db import models
from typing import Optional, List, Dict, Any

class Product(models.Model):
    name: str = models.CharField(max_length=255)
    price: float = models.DecimalField(max_digits=10, decimal_places=2)
    
    def to_typesense_dict(self) -> Dict[str, Any]:
        """Convert product to TypeSense document format"""
        return {
            'id': str(self.id),
            'name': self.name,
            'price': float(self.price),
            # Other fields...
        }
```

### Example 2: View Type Hints

```python
from rest_framework.response import Response
from rest_framework import status
from typing import Dict, Any, Optional

class OrderAPIView(APIView):
    def get(self, request, order_id: Optional[int] = None) -> Response:
        """Get order details or list orders"""
        if order_id:
            # Get specific order
            return Response(data, status=status.HTTP_200_OK)
        else:
            # List orders
            return Response(data, status=status.HTTP_200_OK)
    
    def post(self, request) -> Response:
        """Create a new order"""
        # Process order creation
        return Response(data, status=status.HTTP_201_CREATED)
```

### Example 3: Validator Type Hints

```python
from typing import Dict, Any, List

class OrderValidator:
    def __init__(self, data: Dict[str, Any]):
        self.data = data
        self.errors: Dict[str, str] = {}
    
    def is_valid(self) -> bool:
        """Run all validations and return True if valid"""
        self.validate()
        return len(self.errors) == 0
```

## Creating Custom Management Commands

To create a new management command:

1. Create a new Python file in the `management/commands` directory of your app
2. Extend the `BaseCommand` class from Django
3. Implement the `handle` method

Example:

```python
from django.core.management.base import BaseCommand

class Command(BaseCommand):
    help = 'Description of your command'

    def add_arguments(self, parser):
        parser.add_argument('--option', type=str, help='Description of option')

    def handle(self, *args, **options):
        # Command implementation
        self.stdout.write(self.style.SUCCESS('Command executed successfully'))
```

## Contributing

1. Create a new branch for your feature
2. Make your changes
3. Submit a pull request

## License

This project is licensed under the MIT License.
