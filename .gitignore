# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
*.pot
*.pyc
local_settings.py
db.sqlite3
db.sqlite3-journal
media

# Docker
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Static files
/static/
/media/
/staticfiles/

# Virtual Environment
venv/
ENV/
env/
.venv/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.project
.pydevproject
.settings/

# Database
*.sqlite3
*.db

# Backup files
*.bak
*.tmp
*~

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# AWS
.aws/
.elasticbeanstalk/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Migrations (optional, uncomment if you want to ignore migrations)
# */migrations/*.py
# !*/migrations/__init__.py

# Compiled Python files
*.pyc
*.pyo
*.pyd

# Jupyter Notebook
.ipynb_checkpoints

# Celery
celerybeat-schedule
celerybeat.pid

# Redis
dump.rdb

# macOS specific
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Node (if you use any JavaScript)
node_modules/
npm-debug.log
yarn-error.log
