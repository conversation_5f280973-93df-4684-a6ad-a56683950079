#!/usr/bin/env python
import typesense
import json
import requests

# Typesense client configuration
client = typesense.Client({
    'api_key': 'xyz',
    'nodes': [{
        'host': 'localhost',
        'port': '8108',
        'protocol': 'http'
    }],
    'connection_timeout_seconds': 5
})

def check_health():
    """Check if Typesense is healthy"""
    try:
        # Make a direct HTTP request to the health endpoint
        health_url = "http://localhost:8108/health"
        headers = {"X-TYPESENSE-API-KEY": "xyz"}
        response = requests.get(health_url, headers=headers, timeout=5)
        response.raise_for_status()
        
        health_data = response.json()
        print(f"Health check: {health_data}")
        return health_data.get('ok', False)
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def list_collections():
    """List all collections in Typesense"""
    try:
        collections = client.collections.retrieve()
        print(f"Collections: {json.dumps(collections, indent=2)}")
        return collections
    except Exception as e:
        print(f"Error listing collections: {e}")
        return []

def create_test_document():
    """Create a test document in the products collection"""
    try:
        test_document = {
            'id': 'test-1',
            'name': 'Test Product',
            'description': 'This is a test product for Typesense',
            'sku': 'TEST-001',
            'price': 19.99,
            'brand': 'Test Brand',
            'is_active': True,
            'facility_id': '1',
            'facility_name': 'Test Facility',
            'categories': ['Electronics'],
            'subcategories': ['Smartphones'],
            'created_at': '2025-06-10T00:00:00Z',
            'updated_at': '2025-06-10T00:00:00Z',
            'weight': 0.5,
            'weight_unit': 'kg',
            'image_url': 'https://example.com/test.jpg'
        }
        
        result = client.collections['products'].documents.create(test_document)
        print(f"Created document: {json.dumps(result, indent=2)}")
        return result
    except Exception as e:
        print(f"Error creating document: {e}")
        return None

def search_products(query=""):
    """Search for products in Typesense"""
    try:
        search_parameters = {
            'q': query,
            'query_by': 'name,description,brand',
            'sort_by': 'price:asc'
        }
        
        results = client.collections['products'].documents.search(search_parameters)
        print(f"Search results: {json.dumps(results, indent=2)}")
        return results
    except Exception as e:
        print(f"Error searching: {e}")
        return None

if __name__ == "__main__":
    print("Testing Typesense integration...")
    
    # Check health
    if not check_health():
        print("Typesense is not healthy. Exiting.")
        exit(1)
    
    # List collections
    collections = list_collections()
    
    # Create test document
    create_test_document()
    
    # Search for products
    search_products("test")
    
    print("Test completed!")
