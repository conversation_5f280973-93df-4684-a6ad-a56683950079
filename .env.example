# Django Settings
ENV_DJANGO_SECRET_KEY=your-secret-key-here
DEBUG=False
ENV_HOSTS=your-domain.com,localhost,127.0.0.1

# Sentry Configuration
ENV_SENTRY_DSN=https://<EMAIL>/project-id
ENV_SENTRY_ENVIRONMENT=production
ENV_SENTRY_RELEASE=1.0.0

# CORS Settings (for production)
ENV_CORS_ALLOWED_ORIGINS=https://your-frontend-domain.com
ENV_CORS_ALLOW_CREDENTIALS=True
ENV_CORS_ORIGIN_ALLOW_ALL=False

# Database Configuration
# Add your database configuration here

# Typesense Configuration
TYPESENSE_API_KEY=your-typesense-api-key
TYPESENSE_HOST=typesense
TYPESENSE_PORT=8108
TYPESENSE_PROTOCOL=http

# AWS S3 Configuration
USE_S3=True
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_STORAGE_BUCKET_NAME=
AWS_S3_REGION_NAME=ap-south-1
AWS_S3_CUSTOM_DOMAIN=
